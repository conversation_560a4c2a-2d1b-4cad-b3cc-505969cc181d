package com.laery0.controller;

import com.laery0.po.Book;
import com.laery0.po.BookDetails;
import com.laery0.po.BookReviews;
import com.laery0.service.BookService;
import com.laery0.service.BookServiceImpl;

import java.util.List;

public class BookController {
    private BookService bookService = new BookServiceImpl();

    public List<Book> selectAllBook() {
        return bookService.selectAllBook();
    }

    public List<Book> selectAllBookWithDetails() {
        return bookService.selectAllBookWithDetails();
    }

    public List<BookReviews> selectReviewsByBookId(Integer bookId) {
        return bookService.selectReviewsByBookId(bookId);
    }

    public int addBook(Book book) {
        return bookService.addBook(book);
    }

    public int updateBook(Book book) {
        return bookService.updateBook(book);
    }

    public BookDetails selectBookDetails(Integer bookId) {
        return bookService.selectBookDetails(bookId);
    }

    public List<Book> dynamicQueryBook(Book book) {
        return bookService.dynamicQueryBook(book);
    }

}
