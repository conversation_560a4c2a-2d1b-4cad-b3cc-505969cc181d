package com.laery0.dao;

import com.laery0.po.Book;
import com.laery0.po.BookDetails;
import com.laery0.po.BookReviews;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface BookDao {
    // 查全部书
    @Select("SELECT * FROM books")
    List<Book> selectAllBooks();

    // 根据bookId查明细
    @Select("SELECT * FROM book_details WHERE book_id = #{bookId}")
    BookDetails selectBookDetails(Integer bookId);

    // 根据bookId查评论
    @Select("SELECT * FROM book_reviews WHERE book_id = #{bookId}")
    List<BookReviews> selectBookReviews(Integer bookId);

    // 添加
    @Insert("INSERT INTO books(title, author, price, stock) VALUES(#{title}, #{author}, #{price}, #{stock})")
    int addBook(Book book);

    // 修改
    @Update("UPDATE books SET title=#{title}, author=#{author}, price=#{price}, stock=#{stock} WHERE id=#{id}")
    int updateBook(Book book);

    // 动态条件组合查询
    @Select("<script>" +
            "SELECT * FROM books " +
            "<where> " +
            "   <if test='title != null and title != \"\"'>AND title LIKE CONCAT('%', #{title}, '%')</if>" +
            "   <if test='author != null and author != \"\"'>AND author LIKE CONCAT('%', #{author}, '%')</if>" +
            "   <if test='price != null'>AND price = #{price}</if>" +
            "   <if test='stock != null'>AND stock = #{stock}</if>" +
            "</where>" +
            "</script>")
    List<Book> dynamicQueryBooks(Book book);
}

public interface UserDao {
    // 用户登录
    @Select("SELECT * FROM user WHERE username = #{username} AND password = #{password}")
    User login(@Param("username") String username, @Param("password") String password);
}
