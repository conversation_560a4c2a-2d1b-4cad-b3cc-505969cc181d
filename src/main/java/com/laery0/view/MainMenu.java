package com.laery0.view;

import com.laery0.controller.AdminController;
import com.laery0.controller.BookController;
import com.laery0.po.*;

import java.util.List;
import java.util.Scanner;

public class MainMenu {
    private static AdminController adminController = new AdminController();
    private static BookController bookController = new BookController();

    public static boolean loginView(Scanner scanner) {
        System.out.print("请输入账号:");
        String username = scanner.next();
        System.out.print("请输入密码:");
        String password = scanner.next();
        Admin admin = new Admin();
        admin.setUsername(username);
        admin.setPassword(password);
        ResponseBean<Admin> result = adminController.login(admin);
        switch (result.getStatus()) {
            case 100:
                System.out.println(result.getMessage());
                System.out.println("欢迎" + result.getData().getUsername() + "登录本系统");
                return true;
            case 101:
                System.out.println(result.getMessage());
                return false;
            case 102:
                System.out.println(result.getMessage());
                return false;
            default:
                return false;
        }
    }

    public static void menuView(Scanner scanner) {
        while (true) {
            System.out.println("==== 功能菜单 ====");
            System.out.println("1、查询全部图书");
            System.out.println("2、查询全部图书和明细");
            System.out.println("3、查询图书及其评论");
            System.out.println("4、添加图书");
            System.out.println("5、维护图书信息");
            System.out.println("6、动态查询图书");
            System.out.println("0、退出");
            System.out.print("请选择功能：");
            int choice = scanner.nextInt();
            switch (choice) {
                case 1:
                    queryAllBooks();
                    break;
                case 2:
                    queryAllBooksWithDetails();
                    break;
                case 3:
                    queryBookAndReviews(scanner);
                    break;
                case 4:
                    addBook(scanner);
                    break;
                case 5:
                    updateBook(scanner);
                    break;
                case 6:
                    dynamicQueryBook(scanner);
                    break;
                case 0:
                    System.out.println("退出系统。");
                    return;
                default:
                    System.out.println("无效选择，请重新输入。");
            }
        }
    }

    private static void queryAllBooks() {
        List<Book> books = bookController.selectAllBook();
        System.out.println("id\t书名\t作者\t价格\t库存");
        for (Book book : books) {
            System.out.println(book.getId() + "\t" + book.getTitle() + "\t" + book.getAuthor() + "\t"
                    + book.getPrice() + "\t" + book.getStock());
        }
    }

    private static void queryBookAndReviews(Scanner scanner) {
        System.out.print("请输入要查询评论的图书id：");
        int bookId = scanner.nextInt();
        List<BookReviews> reviews = bookController.selectReviewsByBookId(bookId);
        if (reviews.isEmpty()) {
            System.out.println("该图书没有评论。");
        } else {
            for (BookReviews review : reviews) {
                System.out.println(review);
            }
        }
    }

    private static void addBook(Scanner scanner) {
        System.out.print("请输入书名：");
        String title = scanner.next();
        System.out.print("请输入作者：");
        String author = scanner.next();
        System.out.print("请输入价格：");
        Double price = scanner.nextDouble();
        System.out.print("请输入库存：");
        Integer stock = scanner.nextInt();

        Book book = new Book(title, author, price, stock);
        int rows = bookController.addBook(book);
        if (rows > 0) {
            System.out.println("添加成功！");
        } else {
            System.out.println("添加失败！");
        }
    }

    private static void updateBook(Scanner scanner) {
        System.out.print("请输入要修改的图书id：");
        Integer id = scanner.nextInt();
        System.out.print("请输入新书名：");
        String title = scanner.next();
        System.out.print("请输入新作者：");
        String author = scanner.next();
        System.out.print("请输入新价格：");
        Double price = scanner.nextDouble();
        System.out.print("请输入新库存：");
        Integer stock = scanner.nextInt();

        Book book = new Book(id, title, author, price, stock);
        int rows = bookController.updateBook(book);
        if (rows > 0) {
            System.out.println("修改成功！");
        } else {
            System.out.println("修改失败！");
        }
    }

    private static void queryAllBooksWithDetails() {
        List<Book> books = bookController.selectAllBook();
        System.out.println("id\t书名\t作者\t价格\t库存\t描述\t出版日期");
        for (Book book : books) {
            BookDetails details = bookController.selectBookDetails(book.getId());
            System.out.println(
                    book.getId() + "\t" +
                            book.getTitle() + "\t" +
                            book.getAuthor() + "\t" +
                            book.getPrice() + "\t" +
                            book.getStock() + "\t" +
                            (details != null ? details.getDescription() : "") + "\t" +
                            (details != null ? details.getPublish_Date() : "")
            );
        }
    }

    private static void dynamicQueryBook(Scanner scanner) {
        System.out.print("请输入书名关键字（可为空）：");
        String title = scanner.next();
        System.out.print("请输入作者关键字（可为空）：");
        String author = scanner.next();
        System.out.print("请输入价格（可为空，直接回车跳过）：");
        String priceStr = scanner.next();
        System.out.print("请输入库存（可为空，直接回车跳过）：");
        String stockStr = scanner.next();

        Book book = new Book();
        if (!"".equals(title)) book.setTitle(title);
        if (!"".equals(author)) book.setAuthor(author);
        if (!"".equals(priceStr)) book.setPrice(Double.parseDouble(priceStr));
        if (!"".equals(stockStr)) book.setStock(Integer.parseInt(stockStr));

        List<Book> books = bookController.dynamicQueryBook(book);
        if (books.isEmpty()) {
            System.out.println("没有查到相关图书。");
        } else {
            for (Book b : books) {
                System.out.println(b);
            }
        }
    }
}

