package com.laery0.service;

import com.laery0.dao.AdminDao;
import com.laery0.po.Admin;
import com.laery0.po.ResponseBean;
import com.laery0.utils.MyBatisUtil;
import org.apache.ibatis.session.SqlSession;

public class AdminServiceImpl implements AdminService {
    @Override
    public ResponseBean<Admin> login(Admin admin) {
        try(SqlSession sqlSession = MyBatisUtil.getMybatisSqlSession()) {
            AdminDao adminDao = sqlSession.getMapper(AdminDao.class);
            Admin loginAdmin = adminDao.login(admin);
            ResponseBean<Admin> adminResponseBean = null;
            if (loginAdmin == null) {
                adminResponseBean = new ResponseBean<Admin>(101, "用户名错误");
            } else {
                if (!loginAdmin.getPassword().equals(admin.getPassword())) {
                    adminResponseBean = new ResponseBean<Admin>(102, "密码错误");
                } else {
                    loginAdmin.setPassword(null);
                    adminResponseBean = new ResponseBean<Admin>(loginAdmin, 100, "登录成功");
                }
            }
            return adminResponseBean;
        }
    }
}
