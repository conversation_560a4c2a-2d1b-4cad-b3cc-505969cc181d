package com.laery0.service;

import com.laery0.dao.BookDao;
import com.laery0.po.Book;
import com.laery0.po.BookDetails;
import com.laery0.po.BookReviews;
import com.laery0.utils.MyBatisUtil;
import org.apache.ibatis.session.SqlSession;

import java.util.List;

public class BookServiceImpl implements BookService {
    @Override
    public List<Book> selectAllBook() {
        try(SqlSession sqlSession = MyBatisUtil.getMybatisSqlSession()) {
            BookDao dao = sqlSession.getMapper(BookDao.class);
            return dao.selectAllBook();
        }
    }
    @Override
    public List<BookReviews> selectReviewsByBookId(Integer bookId) {
        try(SqlSession sqlSession = MyBatisUtil.getMybatisSqlSession()) {
            BookDao dao = sqlSession.getMapper(BookDao.class);
            return dao.selectReviewsByBookId(bookId);
        }
    }
    @Override
    public int addBook(Book book) {
        try(SqlSession sqlSession = MyBatisUtil.getMybatisSqlSession()) {
            BookDao dao = sqlSession.getMapper(BookDao.class);
            int rows = dao.addBook(book);
            sqlSession.commit();
            return rows;
        }
    }
    @Override
    public int updateBook(Book book) {
        try(SqlSession sqlSession = MyBatisUtil.getMybatisSqlSession()) {
            BookDao dao = sqlSession.getMapper(BookDao.class);
            int rows = dao.updateBook(book);
            sqlSession.commit();
            return rows;
        }
    }
    @Override
    public List<Book> dynamicQueryBook(Book book) {
        try(SqlSession sqlSession = MyBatisUtil.getMybatisSqlSession()) {
            BookDao dao = sqlSession.getMapper(BookDao.class);
            return dao.dynamicQueryBook(book);
        }
    }
    @Override
    public BookDetails selectBookDetails(Integer bookId) {
        try(SqlSession sqlSession = MyBatisUtil.getMybatisSqlSession()) {
            BookDao dao = sqlSession.getMapper(BookDao.class);
            return dao.selectBookDetails(bookId);
        }
    }
}
