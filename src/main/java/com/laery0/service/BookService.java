package com.laery0.service;

import com.laery0.po.Book;
import com.laery0.po.BookDetails;
import com.laery0.po.BookReviews;

import java.util.List;

public interface BookService {
    List<Book> selectAllBook();
    List<Book> selectAllBookWithDetails();
    List<BookReviews> selectReviewsByBookId(Integer bookId);
    int addBook(Book book);
    int updateBook(Book book);
    List<Book> dynamicQueryBook(Book book);
    BookDetails selectBookDetails(Integer bookId);
}

