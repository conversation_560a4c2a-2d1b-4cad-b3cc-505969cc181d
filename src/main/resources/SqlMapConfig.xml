<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <!-- 如果aggressiveLazyLoading为true，那么lazyLoadingEnabled即使为true也无效。 -->
    <settings>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="aggressiveLazyLoading" value="false"/>
    </settings>
    <typeAliases>
        <package name="com.laery0.po"/>
    </typeAliases>
    <!-- 配置数据源相关属性和事务 -->
    <environments default="mysql">
        <!-- 可以配置多个数据源环境，默认使用default中的值 -->
        <environment id="mysql">
            <!-- 使用jdbc的事务管理 -->
            <transactionManager type="JDBC" />
            <!-- 配置数据源，并使用自带数据库连接池 -->
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.cj.jdbc.Driver" />
                <property name="url"
                          value="*******************************************************************************************************************" />
                <property name="username" value="root" />
                <property name="password" value="root" />
            </dataSource>
        </environment>
    </environments>
    <!-- 配置映射文件，可配置多个 -->
    <mappers>
        <mapper resource="mapper/AdminDao.xml" />
    </mappers>
</configuration>


